const express = require('express');
const bodyParser = require('body-parser');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const app = express();
app.use(bodyParser.json());

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }
  
  next();
});

const PORT = process.env.PORT || 3000;
const CLIENT_ID = process.env.SPOTIFY_CLIENT_ID;
const CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET;
const PLAYLIST_ID = process.env.SPOTIFY_PLAYLIST_ID;
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';
const SPOTIFY_AUTH_PASSWORD = process.env.SPOTIFY_AUTH_PASSWORD || 'spotify123';
const REDIRECT_URI = 'https://tropical-alvera-msorg-b735c0d5.koyeb.app/callback';

let submissions = [];
let blocked = [];
let tokens = {};

// Secure file loading functions
function loadSubmissions() {
  try {
    if (fs.existsSync('submissions.json')) {
      submissions = JSON.parse(fs.readFileSync('submissions.json'));
      console.log(`Loaded ${submissions.length} submissions`);
    } else {
      console.log('No submissions.json found - starting fresh');
      submissions = [];
    }
  } catch (error) {
    console.error('Error loading submissions:', error.message);
    submissions = [];
  }
}

function loadBlocked() {
  try {
    if (fs.existsSync('blocked.json')) {
      blocked = JSON.parse(fs.readFileSync('blocked.json'));
      console.log(`Loaded ${blocked.length} blocked links`);
    } else {
      console.log('No blocked.json found - starting fresh');
      blocked = [];
    }
  } catch (error) {
    console.error('Error loading blocked list:', error.message);
    blocked = [];
  }
}

function loadTokens() {
  try {
    if (fs.existsSync('tokens.json')) {
      tokens = JSON.parse(fs.readFileSync('tokens.json'));
      console.log('✅ Tokens loaded successfully');
    } else {
      console.log('ℹ️  No tokens.json found - will be created during Spotify OAuth');
      tokens = {};
    }
  } catch (error) {
    console.error('❌ Error loading tokens:', error.message);
    tokens = {};
  }
}

// Save all data files securely
function saveData() {
  try {
    fs.writeFileSync('submissions.json', JSON.stringify(submissions, null, 2));
    fs.writeFileSync('blocked.json', JSON.stringify(blocked, null, 2));
    
    // Only save tokens if they exist
    if (tokens && (tokens.access_token || tokens.refresh_token)) {
      fs.writeFileSync('tokens.json', JSON.stringify(tokens, null, 2));
    }
    
  } catch (error) {
    console.error('Error saving data:', error.message);
  }
}

// Initialize data
loadSubmissions();
loadBlocked();
loadTokens();

// Simple session store
let adminSessions = new Set();

// Middleware to check admin authentication
function requireAdmin(req, res, next) {
  const sessionId = req.headers.authorization?.replace('Bearer ', '');
  if (!sessionId || !adminSessions.has(sessionId)) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  next();
}

// Check if Spotify authentication is needed
function isSpotifyAuthNeeded() {
  return !tokens.access_token && !tokens.refresh_token;
}

// Refresh Spotify access token if expired or missing
async function refreshAccessToken() {
  const now = Date.now();
  
  if (tokens.access_token && tokens.expires_at && tokens.expires_at > now + 60000) {
    return tokens.access_token;
  }
  
  if (!tokens.refresh_token) {
    throw new Error('No refresh token stored! Spotify re-authentication required.');
  }

  const params = new URLSearchParams();
  params.append('grant_type', 'refresh_token');
  params.append('refresh_token', tokens.refresh_token);

  const res = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      Authorization: 'Basic ' + Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64'),
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params.toString(),
  });

  if (!res.ok) {
    const text = await res.text();
    throw new Error('Spotify refresh token error: ' + text);
  }

  const data = await res.json();
  tokens.access_token = data.access_token;
  tokens.expires_at = Date.now() + data.expires_in * 1000;
  if (data.refresh_token) tokens.refresh_token = data.refresh_token;
  saveData();

  return tokens.access_token;
}

// Add track to Spotify playlist
async function addTrackToSpotifyPlaylist(trackId) {
  const accessToken = await refreshAccessToken();
  const url = `https://api.spotify.com/v1/playlists/${PLAYLIST_ID}/tracks`;
  const body = { uris: [`spotify:track:${trackId}`] };

  const res = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Spotify API error: ${text}`);
  }
}

// Extract Spotify track ID from URL
function extractSpotifyTrackId(url) {
  const m = url.match(/open\.spotify\.com\/track\/([a-zA-Z0-9]+)/);
  return m ? m[1] : null;
}

// Get track info from Spotify API (including explicit flag)
async function getTrackInfo(trackId) {
  try {
    const accessToken = await refreshAccessToken();
    const res = await fetch(`https://api.spotify.com/v1/tracks/${trackId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (res.ok) {
      const data = await res.json();
      return {
        name: data.name,
        artist: data.artists[0]?.name || 'Unknown',
        album: data.album?.name || 'Unknown',
        explicit: data.explicit || false,
        image: data.album?.images?.[0]?.url || null,
        duration_ms: data.duration_ms || 0,
        popularity: data.popularity || 0
      };
    }
  } catch (e) {
    console.error('Error fetching track info:', e);
  }
  return null;
}

// SPOTIFY OAUTH ROUTES

// Spotify authentication page
const spotifyAuthHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>beaDJ - Spotify Authentication</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        :root {
            --primary: #1db954;
            --primary-dark: #1ed760;
            --secondary: #191414;
            --danger: #e22134;
            --warning: #ff9800;
            --success: #4caf50;
            --background: #121212;
            --surface: #181818;
            --surface-light: #282828;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-muted: #535353;
            --border: #404040;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, var(--background) 0%, #0a0a0a 100%);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-container {
            max-width: 500px;
            width: 100%;
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-light) 100%);
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border);
            text-align: center;
        }

        .auth-container h1 {
            margin: 0 0 20px 0;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
            font-weight: 700;
        }

        .warning-message {
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: var(--warning);
        }

        .auth-message {
            font-size: 1.1em;
            margin: 20px 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .contact-info {
            background: rgba(29, 185, 84, 0.1);
            border: 1px solid rgba(29, 185, 84, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-size: 0.9em;
        }

        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid var(--border);
            border-radius: 8px;
            box-sizing: border-box;
            background: var(--background);
            color: var(--text-primary);
            font-size: 1em;
        }

        input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
        }

        button {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 10px 0;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .spotify-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .spotify-btn:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        }

        .spotify-btn:disabled {
            background: var(--text-muted);
            cursor: not-allowed;
            transform: none;
        }

        .error {
            color: var(--danger);
            margin: 10px 0;
            padding: 10px;
            background: rgba(226, 33, 52, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(226, 33, 52, 0.2);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            gap: 10px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--text-muted);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9em;
        }

        .step.active {
            background: var(--primary);
        }

        .step.completed {
            background: var(--success);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <h1>🎵 beaDJ</h1>
        
        <div class="warning-message">
            <strong>⚠️ Spotify Re-authentication Required</strong>
        </div>

        <div class="auth-message">
            The Spotify authentication tokens have expired or are missing. 
            Administrator access is required to re-authenticate with Spotify.
        </div>

        <div class="contact-info">
            <strong>📧 Please notify Moritz Breier</strong><br>
            Spotify authentication is needed to continue managing the playlist.
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
        </div>

        <div id="passwordStep">
            <input type="password" 
                   id="authPassword" 
                   placeholder="Enter Spotify authentication password"
                   onkeypress="if(event.key==='Enter') verifyPassword()">
            <button onclick="verifyPassword()" class="spotify-btn">🔐 Verify Access</button>
        </div>

        <div id="spotifyStep" style="display: none;">
            <p>Password verified! Click below to authenticate with Spotify:</p>
            <button onclick="authenticateSpotify()" class="spotify-btn">
                🎵 Authenticate with Spotify
            </button>
        </div>

        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        async function verifyPassword() {
            const password = document.getElementById('authPassword').value;
            hideError();

            if (!password) {
                showError('Please enter the authentication password');
                return;
            }

            try {
                const res = await fetch('/spotify-auth/verify', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ password })
                });

                if (res.ok) {
                    // Update UI to show step 2
                    document.getElementById('step1').classList.add('completed');
                    document.getElementById('step1').classList.remove('active');
                    document.getElementById('step2').classList.add('active');
                    
                    document.getElementById('passwordStep').style.display = 'none';
                    document.getElementById('spotifyStep').style.display = 'block';
                } else {
                    showError('Invalid authentication password');
                }
            } catch (e) {
                showError('Authentication failed');
            }
        }

        function authenticateSpotify() {
            // Generate state parameter for security
            const state = Math.random().toString(36).substring(2, 15) + 
                         Math.random().toString(36).substring(2, 15);
            
            // Store state in sessionStorage
            sessionStorage.setItem('spotify_oauth_state', state);
            
            const scopes = 'playlist-modify-private playlist-modify-public';
            const authUrl = 'https://accounts.spotify.com/authorize?' +
                'response_type=code' +
                '&client_id=' + encodeURIComponent('${CLIENT_ID}') +
                '&scope=' + encodeURIComponent(scopes) +
                '&redirect_uri=' + encodeURIComponent('${REDIRECT_URI}') +
                '&state=' + encodeURIComponent(state);
            
            window.location.href = authUrl;
        }
    </script>
</body>
</html>
`;

// Spotify OAuth callback success page
const callbackSuccessHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>beaDJ - Authentication Successful</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #121212 0%, #0a0a0a 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-container {
            max-width: 500px;
            width: 100%;
            background: linear-gradient(135deg, #181818 0%, #282828 100%);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid #404040;
        }

        .success-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        h1 {
            background: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0 0 20px 0;
            font-size: 2em;
        }

        p {
            color: #b3b3b3;
            font-size: 1.1em;
            line-height: 1.6;
            margin: 20px 0;
        }

        .button {
            display: inline-block;
            background: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✅</div>
        <h1>Authentication Successful!</h1>
        <p>Spotify authentication has been completed successfully. The beaDJ application can now manage the playlist.</p>
        <p>You can now close this page and return to the admin panel.</p>
        <a href="/admin" class="button">🎵 Go to Admin Panel</a>
    </div>
</body>
</html>
`;

// Spotify auth routes
app.get('/spotify-auth', (req, res) => {
  res.send(spotifyAuthHTML);
});

app.post('/spotify-auth/verify', (req, res) => {
  const { password } = req.body;
  if (password === SPOTIFY_AUTH_PASSWORD) {
    res.json({ status: 'verified' });
  } else {
    res.status(401).json({ error: 'Invalid password' });
  }
});

app.get('/callback', async (req, res) => {
  const { code, state, error } = req.query;

  if (error) {
    return res.status(400).send(`
      <h1>Spotify Authentication Error</h1>
      <p>Error: ${error}</p>
      <a href="/spotify-auth">Try Again</a>
    `);
  }

  if (!code) {
    return res.status(400).send(`
      <h1>Authentication Failed</h1>
      <p>No authorization code received</p>
      <a href="/spotify-auth">Try Again</a>
    `);
  }

  try {
    // Exchange code for tokens
    const params = new URLSearchParams();
    params.append('grant_type', 'authorization_code');
    params.append('code', code);
    params.append('redirect_uri', REDIRECT_URI);

    const tokenRes = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64'),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    });

    if (!tokenRes.ok) {
      const errorText = await tokenRes.text();
      throw new Error(`Token exchange failed: ${errorText}`);
    }

    const tokenData = await tokenRes.json();
    
    // Store tokens
    tokens = {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_at: Date.now() + tokenData.expires_in * 1000,
      token_type: tokenData.token_type,
      scope: tokenData.scope
    };

    saveData();
    console.log('✅ Spotify authentication successful');

    res.send(callbackSuccessHTML);
  } catch (error) {
    console.error('Spotify OAuth error:', error);
    res.status(500).send(`
      <h1>Authentication Error</h1>
      <p>Failed to complete Spotify authentication: ${error.message}</p>
      <a href="/spotify-auth">Try Again</a>
    `);
  }
});

// ENHANCED ADMIN PANEL HTML with Spotify Auth Check
const adminPanelHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>beaDJ - Admin Panel</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        :root {
            --primary: #1db954;
            --primary-dark: #1ed760;
            --secondary: #191414;
            --danger: #e22134;
            --warning: #ff9800;
            --success: #4caf50;
            --background: #121212;
            --surface: #181818;
            --surface-light: #282828;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-muted: #535353;
            --border: #404040;
            --explicit: #e22134;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, var(--background) 0%, #0a0a0a 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-light) 100%);
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .header h1 {
            margin: 0;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .spotify-auth-warning {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 152, 0, 0.05) 100%);
            border: 1px solid rgba(255, 152, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .spotify-auth-warning h3 {
            color: var(--warning);
            margin: 0 0 15px 0;
            font-size: 1.2em;
        }

        .spotify-auth-warning p {
            color: var(--text-secondary);
            margin: 10px 0;
        }

        .section {
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-light) 100%);
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border);
        }

        .section h2 {
            margin: 0 0 25px 0;
            color: var(--text-primary);
            font-size: 1.5em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .submission {
            border: 1px solid var(--border);
            padding: 25px;
            margin: 15px 0;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--background) 0%, #1a1a1a 100%);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submission:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .submission.approved {
            border-color: var(--success);
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
        }

        .submission.blocked {
            border-color: var(--danger);
            background: linear-gradient(135deg, rgba(226, 33, 52, 0.1) 0%, rgba(226, 33, 52, 0.05) 100%);
        }

        .submission-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .submission-info {
            flex: 1;
            min-width: 300px;
        }

        .submission-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .track-info {
            background: rgba(255, 255, 255, 0.08);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid var(--primary);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .track-image {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            background: var(--surface-light);
        }

        .track-details {
            flex: 1;
        }

        .track-name {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .track-artist {
            color: #e0e0e0;
            font-size: 0.9em;
        }

        .explicit-badge {
            background: var(--explicit);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .track-meta {
            display: flex;
            gap: 15px;
            margin-top: 8px;
            font-size: 0.8em;
            color: #cccccc;
        }

        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            background: var(--text-muted) !important;
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        .approve {
            background: linear-gradient(135deg, var(--success) 0%, #66bb6a 100%);
            color: white;
        }

        .approve:hover:not(:disabled) {
            background: linear-gradient(135deg, #66bb6a 0%, var(--success) 100%);
        }

        .block {
            background: linear-gradient(135deg, var(--danger) 0%, #ef5350 100%);
            color: white;
        }

        .block:hover:not(:disabled) {
            background: linear-gradient(135deg, #ef5350 0%, var(--danger) 100%);
        }

        .unblock {
            background: linear-gradient(135deg, var(--warning) 0%, #ffb74d 100%);
            color: white;
        }

        .unblock:hover:not(:disabled) {
            background: linear-gradient(135deg, #ffb74d 0%, var(--warning) 100%);
        }

        .primary-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .primary-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        }

        .secondary-btn {
            background: linear-gradient(135deg, var(--surface-light) 0%, #3a3a3a 100%);
            color: var(--text-primary);
            border: 1px solid var(--border);
        }

        .secondary-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #3a3a3a 0%, var(--surface-light) 100%);
        }

        .error {
            color: var(--danger);
            margin: 10px 0;
            padding: 10px;
            background: rgba(226, 33, 52, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(226, 33, 52, 0.2);
        }

        .success {
            color: var(--success);
            margin: 10px 0;
            padding: 10px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(76, 175, 80, 0.2);
        }

        .login-form {
            max-width: 400px;
            margin: 100px auto;
            padding: 40px;
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-light) 100%);
            border-radius: 16px;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border);
        }

        .login-form h2 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--text-primary);
        }

        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid var(--border);
            border-radius: 8px;
            box-sizing: border-box;
            background: var(--background);
            color: var(--text-primary);
            font-size: 1em;
        }

        input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
        }

        .loading {
            color: var(--text-muted);
            font-style: italic;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
                flex-direction: column;
                gap: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .section {
                padding: 20px;
            }

            .submission {
                padding: 20px;
            }

            .submission-header {
                flex-direction: column;
                align-items: stretch;
            }

            .track-info {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div id="loginForm" class="login-form">
        <h2>🎵 beaDJ Admin</h2>
        <input type="password" id="password" placeholder="Enter admin password" onkeypress="if(event.key==='Enter') login()">
        <button onclick="login()" class="primary-btn" style="width: 100%; padding: 15px;">Login</button>
        <div id="loginError" class="error"></div>
    </div>

    <div id="adminPanel" style="display: none;">
        <div class="container">
            <div class="header">
                <h1>🎵 beaDJ Admin</h1>
                <div class="header-actions">
                    <button onclick="loadData()" class="primary-btn">🔄 Refresh</button>
                    <button onclick="checkSpotifyAuth()" class="secondary-btn">🎵 Check Spotify</button>
                    <button onclick="logout()" class="block">🚪 Logout</button>
                </div>
            </div>

            <div id="spotifyAuthWarning" class="spotify-auth-warning" style="display: none;">
                <h3>⚠️ Spotify Authentication Required</h3>
                <p>Spotify authentication is needed to approve songs and manage the playlist.</p>
                <p><strong>Please notify Moritz Breier</strong> - Administrator access required</p>
                <button onclick="window.open('/spotify-auth', '_blank')" class="primary-btn">
                    🔐 Re-authenticate Spotify
                </button>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="pendingCount">0</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="approvedCount">0</div>
                    <div class="stat-label">Approved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="blockedCount">0</div>
                    <div class="stat-label">Blocked</div>
                </div>
            </div>

            <div class="section">
                <h2>⏳ Pending Submissions</h2>
                <div id="pendingSubmissions"></div>
            </div>

            <div class="section">
                <h2>✅ Approved Submissions</h2>
                <div id="approvedSubmissions"></div>
            </div>

            <div class="section">
                <h2>🚫 Blocked Links</h2>
                <div id="blockedLinks"></div>
            </div>
        </div>
    </div>

    <script>
        let sessionToken = localStorage.getItem('adminSession');
        let spotifyAuthRequired = false;

        if (sessionToken) {
            checkSession();
        }

        async function checkSession() {
            try {
                const res = await fetch('/admin/verify', {
                    headers: { 'Authorization': 'Bearer ' + sessionToken }
                });
                if (res.ok) {
                    showAdminPanel();
                } else {
                    localStorage.removeItem('adminSession');
                    sessionToken = null;
                }
            } catch (e) {
                localStorage.removeItem('adminSession');
                sessionToken = null;
            }
        }

        async function checkSpotifyAuth() {
            try {
                const res = await apiCall('/admin/spotify-status');
                if (res && res.ok) {
                    const data = await res.json();
                    spotifyAuthRequired = data.authRequired;
                    updateSpotifyWarning();
                }
            } catch (e) {
                console.error('Error checking Spotify auth:', e);
            }
        }

        function updateSpotifyWarning() {
            const warning = document.getElementById('spotifyAuthWarning');
            if (spotifyAuthRequired) {
                warning.style.display = 'block';
                // Disable approve buttons
                document.querySelectorAll('.approve').forEach(btn => {
                    btn.disabled = true;
                    btn.title = 'Spotify authentication required';
                });
            } else {
                warning.style.display = 'none';
                // Enable approve buttons
                document.querySelectorAll('.approve').forEach(btn => {
                    btn.disabled = false;
                    btn.title = '';
                });
            }
        }

        async function login() {
            const password = document.getElementById('password').value;
            try {
                const res = await fetch('/admin/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ password })
                });

                if (res.ok) {
                    const data = await res.json();
                    sessionToken = data.token;
                    localStorage.setItem('adminSession', sessionToken);
                    showAdminPanel();
                } else {
                    document.getElementById('loginError').textContent = 'Invalid password';
                }
            } catch (e) {
                document.getElementById('loginError').textContent = 'Login failed';
            }
        }

        function logout() {
            localStorage.removeItem('adminSession');
            sessionToken = null;
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('adminPanel').style.display = 'none';
        }

        function showAdminPanel() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('adminPanel').style.display = 'block';
            loadData();
            checkSpotifyAuth();
        }

        async function apiCall(url, options = {}) {
            const res = await fetch(url, {
                ...options,
                headers: {
                    ...options.headers,
                    'Authorization': 'Bearer ' + sessionToken,
                    'Content-Type': 'application/json'
                }
            });

            if (res.status === 401) {
                logout();
                return null;
            }

            return res;
        }

        function formatDuration(ms) {
            const minutes = Math.floor(ms / 60000);
            const seconds = Math.floor((ms % 60000) / 1000);
            return \`\${minutes}:\${seconds.toString().padStart(2, '0')}\`;
        }

        async function loadData() {
            try {
                const [pendingRes, approvedRes, blockedRes] = await Promise.all([
                    apiCall('/admin/pending'),
                    apiCall('/admin/approved'),
                    apiCall('/admin/blocked')
                ]);

                if (pendingRes && approvedRes && blockedRes) {
                    const pending = await pendingRes.json();
                    const approved = await approvedRes.json();
                    const blocked = await blockedRes.json();

                    // Update stats
                    document.getElementById('pendingCount').textContent = pending.length;
                    document.getElementById('approvedCount').textContent = approved.length;
                    document.getElementById('blockedCount').textContent = blocked.length;

                    displayPending(pending);
                    displayApproved(approved);
                    displayBlocked(blocked);
                }
            } catch (e) {
                console.error('Error loading data:', e);
            }
        }

        function displayPending(submissions) {
            const container = document.getElementById('pendingSubmissions');
            if (submissions.length === 0) {
                container.innerHTML = '<div class="empty-state">🎵 No pending submissions</div>';
                return;
            }

            container.innerHTML = submissions.map(sub => \`
                <div class="submission">
                    <div class="submission-header">
                        <div class="submission-info">
                            <strong>👤 User:</strong> \${sub.user || 'Anonymous'}<br>
                            <strong>🔗 Link:</strong> <a href="\${sub.link}" target="_blank" style="color: var(--primary);">\${sub.link}</a><br>
                            <strong>📅 Submitted:</strong> \${new Date(sub.id).toLocaleString()}
                        </div>
                        <div class="submission-actions">
                            <button class="approve" onclick="approveSubmission(\${sub.id})" \${spotifyAuthRequired ? 'disabled title="Spotify authentication required"' : ''}>
                                ✅ Approve
                            </button>
                            <button class="block" onclick="blockLink('\${sub.link}')">🚫 Block Link</button>
                        </div>
                    </div>
                    <div id="track-\${sub.id}" class="track-info loading">🎵 Loading track info...</div>
                </div>
            \`).join('');

            // Load track info for each submission
            submissions.forEach(async (sub) => {
                const trackInfo = await getTrackInfo(sub.link);
                const trackDiv = document.getElementById('track-' + sub.id);
                if (trackInfo) {
                    const explicitBadge = trackInfo.explicit ? 
                        '<span class="explicit-badge">Explicit</span>' : '';
                    const image = trackInfo.image ? 
                        \`<img src="\${trackInfo.image}" alt="Album art" class="track-image">\` : 
                        '<div class="track-image" style="display: flex; align-items: center; justify-content: center; background: var(--surface-light); color: var(--text-muted);">🎵</div>';
                    
                    trackDiv.innerHTML = \`
                        \${image}
                        <div class="track-details">
                            <div class="track-name">
                                🎵 \${trackInfo.name}
                                \${explicitBadge}
                            </div>
                            <div class="track-artist">👨‍🎤 \${trackInfo.artist}</div>
                            <div class="track-meta">
                                <span>📀 \${trackInfo.album}</span>
                                <span>⏱️ \${formatDuration(trackInfo.duration_ms)}</span>
                                <span>📊 \${trackInfo.popularity}% popularity</span>
                            </div>
                        </div>
                    \`;
                    trackDiv.classList.remove('loading');
                    
                    if (trackInfo.explicit) {
                        trackDiv.style.borderColor = 'var(--explicit)';
                    }
                } else {
                    trackDiv.innerHTML = '❌ Could not load track info';
                    trackDiv.classList.remove('loading');
                }
            });
        }

        function displayApproved(submissions) {
            const container = document.getElementById('approvedSubmissions');
            if (submissions.length === 0) {
                container.innerHTML = '<div class="empty-state">✅ No approved submissions yet</div>';
                return;
            }

            container.innerHTML = submissions.map(sub => \`
                <div class="submission approved">
                    <strong>👤 User:</strong> \${sub.user || 'Anonymous'}<br>
                    <strong>🔗 Link:</strong> <a href="\${sub.link}" target="_blank" style="color: var(--success);">\${sub.link}</a><br>
                    <strong>✅ Approved:</strong> \${new Date(sub.id).toLocaleString()}
                </div>
            \`).join('');
        }

        function displayBlocked(blocked) {
            const container = document.getElementById('blockedLinks');
            if (blocked.length === 0) {
                container.innerHTML = '<div class="empty-state">🚫 No blocked links</div>';
                return;
            }

            container.innerHTML = blocked.map(link => \`
                <div class="submission blocked">
                    <div class="submission-header">
                        <div class="submission-info">
                            <strong>🔗 Link:</strong> <a href="\${link}" target="_blank" style="color: var(--danger);">\${link}</a>
                        </div>
                        <div class="submission-actions">
                            <button class="unblock" onclick="unblockLink('\${link}')">🔓 Unblock</button>
                        </div>
                    </div>
                </div>
            \`).join('');
        }

        async function getTrackInfo(link) {
            try {
                const res = await apiCall('/admin/track-info?link=' + encodeURIComponent(link));
                if (res && res.ok) {
                    return await res.json();
                }
            } catch (e) {
                console.error('Error getting track info:', e);
            }
            return null;
        }

        async function approveSubmission(id) {
            if (spotifyAuthRequired) {
                alert('Spotify authentication is required to approve submissions. Please contact Moritz Breier.');
                return;
            }

            try {
                const res = await apiCall('/admin/approve/' + id, { method: 'POST' });
                if (res && res.ok) {
                    loadData();
                } else {
                    const error = await res.json();
                    if (error.error && error.error.includes('refresh token')) {
                        spotifyAuthRequired = true;
                        updateSpotifyWarning();
                        alert('Spotify authentication expired. Please re-authenticate.');
                    } else {
                        alert('Error: ' + error.error);
                    }
                }
            } catch (e) {
                alert('Error approving submission');
            }
        }

        async function blockLink(link) {
            try {
                const res = await apiCall('/admin/block', {
                    method: 'POST',
                    body: JSON.stringify({ link })
                });
                if (res && res.ok) {
                    loadData();
                } else {
                    alert('Error blocking link');
                }
            } catch (e) {
                alert('Error blocking link');
            }
        }

        async function unblockLink(link) {
            try {
                const res = await apiCall('/admin/unblock', {
                    method: 'POST',
                    body: JSON.stringify({ link })
                });
                if (res && res.ok) {
                    loadData();
                } else {
                    alert('Error unblocking link');
                }
            } catch (e) {
                alert('Error unblocking link');
            }
        }
    </script>
</body>
</html>
`;

// PUBLIC ROUTES
app.post('/submit', (req, res) => {
  const { user, link } = req.body;
  if (!link) return res.status(400).send({ error: 'No link provided' });

  if (blocked.includes(link)) return res.status(403).send({ error: 'Link blocked' });

  // Check if song already exists (pending or approved)
  const existingSubmission = submissions.find(s => s.link === link);
  if (existingSubmission) {
    if (existingSubmission.approved) {
      return res.status(409).send({ error: 'This song has already been approved and added to the playlist' });
    } else {
      return res.status(409).send({ error: 'This song is already pending approval' });
    }
  }

  submissions.push({ id: Date.now(), user, link, approved: false });
  saveData();
  res.send({ status: 'pending' });
});

app.get('/pending', (req, res) => {
  res.send(submissions.filter(s => !s.approved));
});

app.get('/approved', (req, res) => {
  res.send(submissions.filter(s => s.approved));
});

app.get('/blocks', (req, res) => {
  res.send(blocked);
});

// ADMIN ROUTES
app.get('/admin', (req, res) => {
  res.send(adminPanelHTML);
});

app.post('/admin/login', (req, res) => {
  const { password } = req.body;
  if (password === ADMIN_PASSWORD) {
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    adminSessions.add(token);
    res.json({ token });
  } else {
    res.status(401).json({ error: 'Invalid password' });
  }
});

app.get('/admin/verify', requireAdmin, (req, res) => {
  res.json({ status: 'authenticated' });
});

app.get('/admin/spotify-status', requireAdmin, (req, res) => {
  res.json({ authRequired: isSpotifyAuthNeeded() });
});

app.get('/admin/pending', requireAdmin, (req, res) => {
  res.json(submissions.filter(s => !s.approved));
});

app.get('/admin/approved', requireAdmin, (req, res) => {
  res.json(submissions.filter(s => s.approved));
});

app.get('/admin/blocked', requireAdmin, (req, res) => {
  res.json(blocked);
});

app.get('/admin/track-info', requireAdmin, async (req, res) => {
  const { link } = req.query;
  const trackId = extractSpotifyTrackId(link);
  if (!trackId) {
    return res.status(400).json({ error: 'Invalid Spotify link' });
  }

  try {
    const trackInfo = await getTrackInfo(trackId);
    if (trackInfo) {
      res.json(trackInfo);
    } else {
      res.status(404).json({ error: 'Track info not found' });
    }
  } catch (error) {
    if (error.message.includes('refresh token')) {
      res.status(401).json({ error: 'Spotify authentication required', authRequired: true });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

app.post('/admin/approve/:id', requireAdmin, async (req, res) => {
  const id = Number(req.params.id);
  const sub = submissions.find(s => s.id === id);
  if (!sub) return res.status(404).send({ error: 'Submission not found' });
  
  if (sub.approved) return res.status(400).send({ error: 'Already approved' });

  const trackId = extractSpotifyTrackId(sub.link);
  if (!trackId) return res.status(400).send({ error: 'Not a valid Spotify track link' });

  try {
    await addTrackToSpotifyPlaylist(trackId);
    sub.approved = true;
    saveData();
    res.send({ status: 'approved', trackId });
  } catch (e) {
    if (e.message.includes('refresh token')) {
      res.status(401).send({ error: 'Spotify authentication required: ' + e.message, authRequired: true });
    } else {
      res.status(500).send({ error: e.message });
    }
  }
});

app.post('/admin/block', requireAdmin, (req, res) => {
  const { link } = req.body;
  if (!link) return res.status(400).json({ error: 'No link provided' });

  if (!blocked.includes(link)) {
    blocked.push(link);
    saveData();
  }

  // Also remove from pending submissions
  submissions = submissions.filter(s => s.link !== link);
  saveData();

  res.json({ status: 'blocked' });
});

app.post('/admin/unblock', requireAdmin, (req, res) => {
  const { link } = req.body;
  const index = blocked.indexOf(link);
  if (index > -1) {
    blocked.splice(index, 1);
    saveData();
  }
  res.json({ status: 'unblocked' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Admin panel available at: http://localhost:${PORT}/admin`);
  console.log(`Admin password: ${ADMIN_PASSWORD}`);
  console.log(`Spotify auth password: ${SPOTIFY_AUTH_PASSWORD}`);
  console.log(`Spotify auth page: http://localhost:${PORT}/spotify-auth`);
  
  if (isSpotifyAuthNeeded()) {
    console.log('⚠️  Spotify authentication required - visit /spotify-auth to authenticate');
  } else {
    console.log('✅ Spotify tokens loaded successfully');
  }
});
